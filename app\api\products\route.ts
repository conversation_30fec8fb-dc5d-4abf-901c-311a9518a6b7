import { NextResponse } from 'next/server'
import { 
  ArrowRight, Cloud, Lock, BarChart, Headphones, 
  Boxes, Zap, Globe, CheckCircle2, Shield, Key, 
  GraduationCap, Database, Eye, LineChart 
} from "lucide-react"

const products = [
  {
    slug: "enterprise-suite",
    name: "企业套件",
    description: "为大型组织提供完整的业务解决方案",
    iconName: "Boxes",
    features: [
      { text: "高级数据分析", iconName: "BarChart" },
      { text: "24/7 技术支持", iconName: "Headphones" },
      { text: "定制化集成", iconName: "Boxes" }
    ],
    highlight: "最受欢迎"
  },
  {
    slug: "exam-system",
    name: "在线考试系统",
    description: "为教育机构提供专业的在线考试解决方案",
    iconName: "GraduationCap",
    features: [
      { text: "智能题库管理", iconName: "Database" },
      { text: "实时监考系统", iconName: "Eye" },
      { text: "成绩分析报告", iconName: "LineChart" }
    ],
    highlight: "教育精选"
  },
  {
    slug: "cloud-platform",
    name: "云平台",
    description: "为现代应用提供可扩展的云基础设施",
    iconName: "Cloud",
    features: [
      { text: "自动扩展", iconName: "Zap" },
      { text: "全球CDN", iconName: "Globe" },
      { text: "99.9%可用性", iconName: "CheckCircle2" }
    ]
  },
  {
    slug: "security-shield",
    name: "安全防护",
    description: "为您的数字资产提供全方位的安全保护",
    iconName: "Shield",
    features: [
      { text: "威胁检测", iconName: "Shield" },
      { text: "数据加密", iconName: "Lock" },
      { text: "访问控制", iconName: "Key" }
    ]
  }
]

export async function GET() {
  return NextResponse.json(products)
} 