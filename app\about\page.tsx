"use client"

import { motion } from "framer-motion"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Brain, Cpu, GraduationCap, Phone, Mail, MapPin, Target, Users, Award, TrendingUp } from "lucide-react"
import Link from "next/link"

// 核心业务介绍
const coreBusinesses = [
  {
    title: "AI智能标注",
    content: "提供专业的AI数据标注服务，支持图像、文本、语音等多模态数据标注，拥有500+专业标注团队，为机器学习模型提供高质量训练数据。",
    icon: Brain,
    stats: "1000万+ 标注数据",
    features: ["图像标注", "文本标注", "语音标注", "质量控制"]
  },
  {
    title: "CPU算力租用",
    content: "灵活的云计算资源租用服务，提供高性能CPU集群，支持科学计算、深度学习训练等高算力需求，按需付费，弹性扩容。",
    icon: Cpu,
    stats: "50,000+ CPU核心",
    features: ["高性能计算", "弹性扩容", "按需付费", "24/7监控"]
  },
  {
    title: "教育培训管理",
    content: "一站式教育培训管理平台，涵盖课程管理、学员管理、在线考试、证书颁发等完整教育生态，服务50,000+学员。",
    icon: GraduationCap,
    stats: "50,000+ 服务学员",
    features: ["课程管理", "在线考试", "学员跟踪", "证书系统"]
  }
]

// 企业优势
const advantages = [
  {
    title: "专业团队",
    description: "拥有200+技术专家，覆盖AI、云计算、教育科技等多个领域",
    icon: Users,
    stats: "200+ 专家"
  },
  {
    title: "服务客户",
    description: "已为2000+企业客户提供专业服务，涵盖多个行业",
    icon: Target,
    stats: "2000+ 客户"
  },
  {
    title: "行业认证",
    description: "获得ISO27001、SOC2等多项国际认证，确保服务质量",
    icon: Award,
    stats: "10+ 认证"
  },
  {
    title: "业务增长",
    description: "连续3年保持100%+业务增长，行业领先地位稳固",
    icon: TrendingUp,
    stats: "100%+ 增长"
  }
]

// 联系方式
const contactInfo = {
  phone: {
    label: "全国服务热线",
    value: "************",
    icon: Phone,
  },
  email: {
    label: "商务合作邮箱",
    value: "<EMAIL>",
    icon: Mail,
  },
  address: {
    label: "总部地址",
    value: "北京市朝阳区科技园区88号零点大厦",
    icon: MapPin,
  }
}

export default function About() {
  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="relative py-24 sm:py-32 overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-primary/5 to-primary/10" />
        <div className="relative mx-auto max-w-7xl px-6 lg:px-8">
          <div className="mx-auto max-w-2xl text-center">
            <motion.h1
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
              className="text-4xl font-bold tracking-tight sm:text-6xl"
            >
              关于零点科技
            </motion.h1>
            <motion.p
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.2 }}
              className="mt-6 text-lg leading-8 text-muted-foreground"
            >
              专注AI智能标注、CPU算力租用、教育培训管理三大核心业务，为企业数字化转型提供专业技术服务
            </motion.p>
          </div>
        </div>
      </section>

      {/* Core Businesses Section */}
      <section className="py-24 sm:py-32">
        <div className="mx-auto max-w-7xl px-6 lg:px-8">
          <div className="mx-auto max-w-2xl text-center">
            <h2 className="text-3xl font-bold tracking-tight sm:text-4xl">
              核心业务
            </h2>
            <p className="mt-6 text-lg leading-8 text-muted-foreground">
              三大核心业务领域，为不同行业客户提供专业解决方案
            </p>
          </div>
          <div className="mx-auto mt-16 grid max-w-2xl grid-cols-1 gap-8 lg:max-w-none lg:grid-cols-3">
            {coreBusinesses.map((business, index) => (
              <motion.div
                key={business.title}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                viewport={{ once: true }}
              >
                <Card className="h-full hover:shadow-lg transition-shadow duration-300">
                  <CardContent className="p-8">
                    <div className="flex items-center gap-4 mb-6">
                      <div className="rounded-lg bg-primary/10 p-3">
                        <business.icon className="h-8 w-8 text-primary" />
                      </div>
                      <div>
                        <h3 className="text-xl font-semibold">{business.title}</h3>
                        <p className="text-sm text-primary font-medium">{business.stats}</p>
                      </div>
                    </div>
                    <p className="text-muted-foreground mb-6">{business.content}</p>
                    <div className="flex flex-wrap gap-2">
                      {business.features.map((feature) => (
                        <span
                          key={feature}
                          className="inline-flex items-center rounded-full bg-primary/10 px-3 py-1 text-xs font-medium text-primary"
                        >
                          {feature}
                        </span>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Advantages Section */}
      <section className="py-24 sm:py-32 bg-muted/30">
        <div className="mx-auto max-w-7xl px-6 lg:px-8">
          <div className="mx-auto max-w-2xl text-center">
            <h2 className="text-3xl font-bold tracking-tight sm:text-4xl">
              企业优势
            </h2>
            <p className="mt-6 text-lg leading-8 text-muted-foreground">
              专业实力与服务品质的完美结合
            </p>
          </div>
          <div className="mx-auto mt-16 grid max-w-2xl grid-cols-1 gap-8 lg:max-w-none lg:grid-cols-4">
            {advantages.map((advantage, index) => (
              <motion.div
                key={advantage.title}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                viewport={{ once: true }}
                className="text-center"
              >
                <div className="mx-auto mb-4 rounded-lg bg-primary/10 p-4 w-fit">
                  <advantage.icon className="h-8 w-8 text-primary" />
                </div>
                <h3 className="text-lg font-semibold">{advantage.title}</h3>
                <p className="text-2xl font-bold text-primary mt-2">{advantage.stats}</p>
                <p className="mt-2 text-sm text-muted-foreground">
                  {advantage.description}
                </p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Contact Section */}
      <section className="py-24 sm:py-32">
        <div className="mx-auto max-w-7xl px-6 lg:px-8">
          <div className="mx-auto max-w-2xl text-center">
            <h2 className="text-3xl font-bold tracking-tight sm:text-4xl">
              联系我们
            </h2>
            <p className="mt-6 text-lg leading-8 text-muted-foreground">
              期待与您合作，共创美好未来
            </p>
          </div>
          
          {/* Contact Cards */}
          <div className="mt-16 grid grid-cols-1 gap-8 sm:grid-cols-3">
            {Object.values(contactInfo).map((info, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                viewport={{ once: true }}
              >
                <Card className="text-center hover:shadow-lg transition-shadow duration-300">
                  <CardContent className="pt-8 pb-8">
                    <div className="mx-auto mb-4 rounded-lg bg-primary/10 p-3 w-fit">
                      <info.icon className="h-6 w-6 text-primary" />
                    </div>
                    <h3 className="text-sm font-semibold text-muted-foreground mb-2">
                      {info.label}
                    </h3>
                    <p className="text-base font-medium">{info.value}</p>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>

          {/* CTA Buttons */}
          <div className="mt-16 flex items-center justify-center gap-x-6">
            <Button size="lg" asChild>
              <Link href="/products">浏览产品</Link>
            </Button>
            <Button variant="outline" size="lg" asChild>
              <Link href="/contact-us">联系销售</Link>
            </Button>
          </div>
        </div>
      </section>
    </div>
  )
}
