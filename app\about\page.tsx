import { Button } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Users, Rocket, Target, Award, Phone, Mail, MapPin } from "lucide-react"
import Link from "next/link"


// 添加新的内容分段常量
const introduction = {
  main: "作为一家深耕于创新开发领域的先锋科技企业，零点科技始终秉持着“技术引领未来，创新驱动发展”的核心理念，致力于成为全球科技变革的推动者。",
  details: [
    {
      title: "创新研发",
      content: "公司汇聚了来自世界各地的顶尖技术人才与行业专家，我们在人工智能、大数据、云计算、联网、区块链等前沿技术领域不断探索与突破。"
    },
    {
      title: "行业赋能",
      content: "通过跨界融合与协同创新，为教育、医疗、金融、智慧城市、智能制造等多个行业提供定制化、智能化的解决方案，助力传统产业升级转型。"
    },
    {
      title: "社会责任",
      content: "积极履行社会责任，关注环境保护与可持续发展，通过开发绿色能源管理系统、智能垃圾分类解决方案等，助力构建低碳、环保、智慧的城市生活。"
    }
  ]
}

// 添加联系方式常量
const contactInfo = {
  phone: {
    label: "全国服务热线",
    value: "************",
    icon: Phone,
  },
  email: {
    label: "商务合作邮箱",
    value: "<EMAIL>",
    icon: Mail,
  },
  address: {
    label: "总部地址",
    value: "北京市朝阳区科技园区88号零点大厦",
    icon: MapPin,
  }
}

// 添加动画效果的工具函数
const fadeInAnimation = "animate-in fade-in duration-700 slide-in-from-bottom-4"

export default function About() {
  return (
    <div className="relative isolate">
      {/* 增强背景渐变效果 */}
      <div
        className="absolute inset-x-0 top-0 -z-10 h-[1000px] transform-gpu overflow-hidden blur-3xl"
        aria-hidden="true"
      >
        <div
          className="relative left-[calc(50%-11rem)] aspect-[1155/678] w-[36.125rem] -translate-x-1/2 rotate-[30deg] bg-gradient-to-tr from-[#ff80b5] to-[#9089fc] opacity-30 sm:left-[calc(50%-30rem)] sm:w-[72.1875rem] animate-pulse"
          style={{
            clipPath:
              'polygon(74.1% 44.1%, 100% 61.6%, 97.5% 26.9%, 85.5% 0.1%, 80.7% 2%, 72.5% 32.5%, 60.2% 62.4%, 52.4% 68.1%, 47.5% 58.3%, 45.2% 34.5%, 27.5% 76.7%, 0.1% 64.9%, 17.9% 100%, 27.6% 76.8%, 76.1% 97.7%, 74.1% 44.1%)',
          }}
        />
      </div>

      {/* Hero section with enhanced layout */}
      <div className="py-24 sm:py-32">
        <div className="mx-auto max-w-7xl px-6 lg:px-8">
          <div className={`mx-auto max-w-2xl lg:mx-0 lg:max-w-none ${fadeInAnimation}`}>
            <h1 className="text-4xl font-bold tracking-tight sm:text-6xl bg-gradient-to-r from-primary to-primary/50 bg-clip-text text-transparent relative">
              关于零点(0dot)
              <div className="absolute -bottom-2 left-0 w-20 h-1 bg-gradient-to-r from-primary to-transparent" />
            </h1>
            <div className="mt-6 flex flex-col gap-x-8 gap-y-20 lg:flex-row">
              <div className="lg:w-full lg:max-w-2xl lg:flex-auto">
                <p className="text-xl leading-8 text-muted-foreground">
                  {introduction.main}
                </p>
                <div className="mt-10 space-y-8 text-base leading-7 text-muted-foreground">
                  {introduction.details.map((detail, index) => (
                    <div 
                      key={index} 
                      className="group p-6 rounded-xl bg-gradient-to-br from-muted/50 to-transparent hover:from-muted/80 transition-all duration-300"
                    >
                      <h3 className="font-semibold text-lg text-foreground group-hover:text-primary transition-colors flex items-center gap-2">
                        <span className="inline-block w-2 h-2 rounded-full bg-primary/50 group-hover:w-4 transition-all" />
                        {detail.title}
                      </h3>
                      <p className="mt-3 group-hover:text-foreground transition-colors">
                        {detail.content}
                      </p>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Enhanced CTA section with improved contact info */}
      <div className="relative isolate mt-24 px-6 py-32 sm:mt-40 sm:py-40 lg:px-8 overflow-hidden">
        <div className="absolute inset-0 -z-10 bg-gradient-to-br from-muted/50 to-muted/20 backdrop-blur-xl" />
        <div className="absolute inset-0 -z-10 bg-[radial-gradient(45rem_50rem_at_top,theme(colors.primary.100),transparent)]" />
        <div className={`mx-auto max-w-2xl text-center relative ${fadeInAnimation}`}>
          <h2 className="text-3xl font-bold tracking-tight sm:text-4xl bg-gradient-to-r from-primary to-primary/50 bg-clip-text text-transparent">
            用技术改变世界，让未来生活更加美好
          </h2>
          <p className="mx-auto mt-6 max-w-xl text-lg leading-8 text-muted-foreground">
            加入零点科技，与我们一起开创智能科技新未来
          </p>
          
          {/* Enhanced contact cards */}
          <div className="mt-10 grid grid-cols-1 gap-6 sm:grid-cols-3">
            {Object.values(contactInfo).map((info, index) => (
              <Card 
                key={index}
                className="group hover:shadow-xl transition-all duration-500 border-0 bg-gradient-to-br from-muted/50 to-muted hover:scale-105"
              >
                <CardContent className="pt-6 relative overflow-hidden">
                  <div className="absolute inset-0 bg-gradient-to-br from-primary/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500" />
                  <div className="inline-flex p-3 rounded-xl bg-gradient-to-br from-primary/20 to-primary/10 text-primary group-hover:scale-110 transition-transform duration-300">
                    <info.icon className="h-6 w-6" />
                  </div>
                  <h3 className="mt-4 text-sm font-semibold text-muted-foreground">
                    {info.label}
                  </h3>
                  <p className="mt-2 text-base font-medium group-hover:text-primary transition-colors">
                    {info.value}
                  </p>
                </CardContent>
              </Card>
            ))}
          </div>

          {/* Enhanced buttons */}
          <div className="mt-10 flex items-center justify-center gap-x-6">
            <Button 
              size="lg" 
              asChild
              className="bg-gradient-to-r from-primary to-primary/80 hover:from-primary/90 hover:to-primary/70 hover:scale-105 transition-all duration-300"
            >
              <Link href="/products">浏览产品</Link>
            </Button>
            <Button 
              variant="outline" 
              size="lg"
              className="border-primary/20 hover:border-primary/40 hover:scale-105 transition-all duration-300"
              // onClick={() => window.location.href = `mailto:${contactInfo.email.value}`}
            >
              联系销售
            </Button>
          </div>
        </div>
      </div>
    </div>
  )
}