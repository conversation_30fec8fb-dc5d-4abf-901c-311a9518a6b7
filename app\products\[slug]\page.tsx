import { notFound } from "next/navigation"
import { ProductDetail } from "./components/ProductDetail"
import { Metadata } from 'next'

type Benefit = {
  title: string;
  description: string;
}

type Product = {
  name: string;
  description: string;
  features: string[];
  techSpecs: {
    deployment: string;
    security: string;
    availability: string;
    support: string;
  };
  demoVideo?: {
    url: string;
    thumbnail?: string;
  };
  benefits?: Benefit[];
}

async function getProduct(slug: string): Promise<Product | null> {
  try {
    const res = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/api/products/${slug}`, {
      next: { revalidate: 3600 } // 每小时重新验证一次
    })
    if (!res.ok) return null
    return res.json()
  } catch (error) {
    console.error('Failed to fetch product:', error)
    return null
  }
}

async function getAllProductSlugs() {
  try {
    const res = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/api/products`)
    if (!res.ok) return []
    const products = await res.json()
    return products.map((product: { slug: string }) => product.slug)
  } catch (error) {
    console.error('Failed to fetch product slugs:', error)
    return []
  }
}

export async function generateStaticParams() {
  const slugs = await getAllProductSlugs()
  return slugs.map((slug:any) => ({
    slug: slug,
  }))
}


export async function generateMetadata(
  { params }: { params: { slug: string } }
): Promise<Metadata> {
  const product = await getProduct(params.slug)
  
  if (!product) {
    return {
      title: '产品未找到',
    }
  }

  return {
    title: `${product.name} - 零点科技`,
    description: product.description,
  }
}

export default async function ProductPage({ params }: { params: { slug: string } }) {
  const product = await getProduct(params.slug)

  if (!product) {
    notFound()
  }

  return <ProductDetail product={product} />
}