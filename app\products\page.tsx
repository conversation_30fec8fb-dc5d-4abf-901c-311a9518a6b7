import { ProductCard } from "./components/ProductCard"

// 添加动画效果的工具函数
const fadeInAnimation = "animate-in fade-in duration-700 slide-in-from-bottom-4"

async function getProducts() {
  const res = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/api/products`, {
    next: { revalidate: 3600 } // 每小时重新验证一次
  })
  if (!res.ok) throw new Error('Failed to fetch products')
  return res.json()
}

export default async function Products() {
  const products = await getProducts()

  return (
    <div className="relative isolate py-24 sm:py-32">
      {/* 添加背景效果 */}
      <div
        className="absolute inset-x-0 top-0 -z-10 h-[1000px] transform-gpu overflow-hidden blur-3xl"
        aria-hidden="true"
      >
        <div
          className="relative left-[calc(50%-11rem)] aspect-[1155/678] w-[36.125rem] -translate-x-1/2 rotate-[30deg] bg-gradient-to-tr from-[#ff80b5] to-[#9089fc] opacity-30 sm:left-[calc(50%-30rem)] sm:w-[72.1875rem] animate-pulse"
          style={{
            clipPath:
              'polygon(74.1% 44.1%, 100% 61.6%, 97.5% 26.9%, 85.5% 0.1%, 80.7% 2%, 72.5% 32.5%, 60.2% 62.4%, 52.4% 68.1%, 47.5% 58.3%, 45.2% 34.5%, 27.5% 76.7%, 0.1% 64.9%, 17.9% 100%, 27.6% 76.8%, 76.1% 97.7%, 74.1% 44.1%)',
          }}
        />
      </div>

      <div className="mx-auto max-w-7xl px-6 lg:px-8">
        <div className={`mx-auto max-w-2xl text-center ${fadeInAnimation}`}>
          <h2 className="text-3xl font-bold tracking-tight sm:text-4xl bg-gradient-to-r from-primary to-primary/50 bg-clip-text text-transparent relative inline-block">
            产品服务
            <div className="absolute -bottom-2 left-1/2 w-20 h-1 bg-gradient-to-r from-primary to-transparent -translate-x-1/2" />
          </h2>
          <p className="mt-4 text-lg leading-8 text-muted-foreground">
            发现为您量身定制的创新解决方案
          </p>
        </div>
        
        <div className="mx-auto mt-16 grid max-w-2xl grid-cols-1 gap-6 sm:mt-20 lg:mx-0 lg:max-w-none lg:grid-cols-2 xl:grid-cols-4 lg:gap-8">
          {products.map((product: any, index: number) => (
            <ProductCard 
              key={product.slug} 
              product={product} 
              index={index} 
            />
          ))}
        </div>
      </div>
    </div>
  )
}