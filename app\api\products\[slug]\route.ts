import { NextResponse } from 'next/server'


const products = {
  "enterprise-suite": {
    name: "Enterprise Suite",
    description: "Complete business solution for large organizations",
    features: [
      "Advanced Analytics",
      "24/7 Support", 
      "Custom Integration",
      "Dedicated Account Manager",
      "SLA Guarantees",
      "Custom Reporting",
      "API Access",
      "Unlimited Users",
      "Advanced Security Features"
    ],
    techSpecs: {
      deployment: "Cloud or On-premises",
      security: "SOC 2 Type II, HIPAA, GDPR Compliant",
      availability: "99.99% uptime SLA",
      support: "24/7 Priority Support",
    },
    featureList: [
      {
        title: "核心功能",
        description: "为企业提供全方位的基础功能支持",
        features: [
          {
            name: "多层架构",
            description: "采用先进的多层架构设计，确保系统的可扩展性和维护性",
            icon: "Layers"
          },
          {
            name: "安全防护", 
            description: "全方位的安全防护机制，保护您的数据安全",
            icon: "Shield"
          },
          {
            name: "性能优化",
            description: "智能性能优化，确保系统高效运行",
            icon: "Zap"
          }
        ]
      },
      {
        title: "管理功能",
        description: "强大的管理工具助力企业高效运营",
        features: [
          {
            name: "系统配置",
            description: "灵活的系统配置选项，满足不同场景需求",
            icon: "Settings"
          },
          {
            name: "用户管理",
            description: "完善的用户权限管理，精确控制访问权限",
            icon: "Users"
          },
          {
            name: "数据分析",
            description: "强大的数据分析工具，助力决策制定",
            icon: "BarChart"
          }
        ]
      }
    ],
    demoVideo: {
      url: "https://player.vimeo.com/video/824804225",
      thumbnail: "https://images.unsplash.com/photo-1664575198308-3959904fa430?q=80&w=2940&auto=format&fit=crop",
    },
  },
  // ... 其他产品数据
}

export async function GET(
  request: Request,
  { params }: { params: { slug: string } }
) {
  const product = products[params.slug as keyof typeof products]
  
  if (!product) {
    return new NextResponse('Product not found', { status: 404 })
  }

  return NextResponse.json(product)
}

export function generateStaticParams() {
  return Object.keys(products).map((slug) => ({
    slug: slug,
  }))
}

