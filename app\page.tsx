"use client"

import { But<PERSON> } from "@/components/ui/button"
import { ArrowRight, CircleDot, Brain, Cpu, GraduationCap } from "lucide-react"
import Link from "next/link"
import Image from "next/image"
import { motion } from "framer-motion"

const features = [
  {
    name: 'AI智能标注',
    description: '提供高精度的数据标注服务，支持图像、文本、语音等多模态数据，为机器学习模型训练提供优质数据集。',
    icon: Brain,
    details: ['图像标注', '文本标注', '语音标注', '视频标注', '3D点云标注']
  },
  {
    name: 'CPU算力租用',
    description: '灵活的云计算资源租用服务，提供高性能CPU集群，支持科学计算、深度学习训练等高算力需求场景。',
    icon: Cpu,
    details: ['高性能计算', '弹性扩容', '按需付费', '24/7监控', '数据安全']
  },
  {
    name: '教育培训管理',
    description: '一站式教育培训管理平台，涵盖课程管理、学员管理、考试系统、证书颁发等完整教育生态链。',
    icon: GraduationCap,
    details: ['课程管理', '在线考试', '学员跟踪', '证书系统', '数据分析']
  }
]

const testimonials = [
  {
    content: "0dot的AI智能标注服务大大提升了我们的数据处理效率，标注精度高达99.5%，为我们的机器学习项目节省了大量时间和成本。",
    author: "张明",
    role: "AI研发总监",
    company: "智能科技有限公司",
    image: "https://picsum.photos/seed/user1/200/200"
  },
  {
    content: "使用0dot的CPU租用服务进行深度学习训练，性能稳定，价格合理。弹性扩容功能让我们能够根据项目需求灵活调整资源。",
    author: "李华",
    role: "技术架构师",
    company: "数据科学研究院",
    image: "https://picsum.photos/seed/user2/200/200"
  },
  {
    content: "教育培训管理系统功能全面，界面友好。在线考试系统特别出色，支持多种题型，防作弊功能强大，大大提升了我们的教学管理效率。",
    author: "王芳",
    role: "教务主任",
    company: "现代教育集团",
    image: "https://picsum.photos/seed/user3/200/200"
  }
]

const stats = [
  { label: '标注数据量', value: '1000万+' },
  { label: 'CPU核心数', value: '50,000+' },
  { label: '服务客户', value: '2,000+' },
  { label: '系统可用性', value: '99.9%' }
]

const solutions = [
  {
    title: 'AI数据标注',
    description: '专业的AI数据标注服务，支持图像、文本、语音等多模态数据，为机器学习提供高质量训练数据。',
    image: 'https://picsum.photos/seed/ai-annotation/800/600',
    features: ['图像识别标注', '自然语言处理', '语音识别标注', '视频内容标注']
  },
  {
    title: '云计算资源',
    description: '提供高性能CPU集群租用服务，支持科学计算、深度学习训练等高算力需求场景。',
    image: 'https://picsum.photos/seed/cloud-computing/800/600',
    features: ['弹性扩容', '按需付费', '高可用性', '安全可靠']
  },
  {
    title: '教育管理平台',
    description: '全方位的教育培训管理解决方案，涵盖课程管理、在线考试、学员跟踪等完整功能。',
    image: 'https://picsum.photos/seed/education-platform/800/600',
    features: ['课程管理', '在线考试', '学员管理', '数据分析']
  }
]

const FeatureCard = ({ feature }: { feature: typeof features[0] }) => (
  <motion.div 
    className="flex flex-col hover:translate-y-[-4px] transition-all duration-300 p-6 rounded-2xl bg-muted/30 hover:bg-muted/50"
    initial={{ opacity: 0, y: 20 }}
    whileInView={{ opacity: 1, y: 0 }}
    transition={{ duration: 0.5 }}
    viewport={{ once: true }}
  >
    <dt className="flex items-center gap-x-3 text-base font-semibold leading-7">
      <div className="rounded-lg bg-primary/10 p-2">
        <feature.icon className="h-5 w-5 text-primary" aria-hidden="true" />
      </div>
      <span className="bg-gradient-to-r from-primary to-primary/60 bg-clip-text text-transparent">
        {feature.name}
      </span>
    </dt>
    <dd className="mt-4 flex flex-auto flex-col text-base leading-7">
      <p className="flex-auto text-muted-foreground">{feature.description}</p>
      <div className="mt-6 flex flex-wrap gap-2">
        {feature.details.map((detail) => (
          <span 
            key={detail}
            className="inline-flex items-center rounded-full bg-primary/10 px-3 py-1 text-xs font-medium text-primary"
          >
            {detail}
          </span>
        ))}
      </div>
    </dd>
  </motion.div>
)

const SolutionCard = ({ solution }: { solution: typeof solutions[0] }) => (
  <motion.div
    className="relative overflow-hidden rounded-2xl group"
    initial={{ opacity: 0, scale: 0.9 }}
    whileInView={{ opacity: 1, scale: 1 }}
    transition={{ duration: 0.5 }}
    viewport={{ once: true }}
  >
    <div className="aspect-[4/3] relative">
      <Image
        src={solution.image}
        alt={solution.title}
        fill
        className="object-cover transition-transform duration-300 group-hover:scale-110"
      />
      <div className="absolute inset-0 bg-gradient-to-t from-black/80 to-black/0" />
    </div>
    <div className="absolute bottom-0 p-6 text-white">
      <h3 className="text-xl font-semibold mb-2">{solution.title}</h3>
      <p className="text-sm text-white/80 mb-4">{solution.description}</p>
      <div className="flex flex-wrap gap-2">
        {solution.features.map((feature) => (
          <span
            key={feature}
            className="inline-flex items-center rounded-full bg-white/20 backdrop-blur-sm px-2 py-1 text-xs font-medium text-white"
          >
            {feature}
          </span>
        ))}
      </div>
    </div>
  </motion.div>
)

const TestimonialCard = ({ testimonial }: { testimonial: typeof testimonials[0] }) => (
  <motion.figure 
    className="rounded-2xl bg-muted p-8 transition-colors hover:bg-muted/80"
    initial={{ opacity: 0, x: -20 }}
    whileInView={{ opacity: 1, x: 0 }}
    transition={{ duration: 0.5 }}
    viewport={{ once: true }}
  >
    <blockquote className="text-lg font-semibold leading-8">
      <p>{testimonial.content}</p>
    </blockquote>
    <figcaption className="mt-6 flex items-center gap-x-4">
      <Image
        className="h-10 w-10 rounded-full"
        src={testimonial.image}
        alt=""
        width={40}
        height={40}
      />
      <div>
        <div className="font-semibold">{testimonial.author}</div>
        <div className="text-sm text-muted-foreground">
          {testimonial.role}，{testimonial.company}
        </div>
      </div>
    </figcaption>
  </motion.figure>
)

export default function Home() {
  return (
    <>
      {/* Hero Section */}
      <div className="relative isolate">
        <div
          className="absolute inset-x-0 -top-40 -z-10 transform-gpu overflow-hidden blur-3xl sm:-top-80"
          aria-hidden="true"
        >
          <div
            className="relative left-[calc(50%-11rem)] aspect-[1155/678] w-[36.125rem] -translate-x-1/2 rotate-[30deg] bg-gradient-to-tr from-[#ff80b5] to-[#9089fc] opacity-30 sm:left-[calc(50%-30rem)] sm:w-[72.1875rem]"
            style={{
              clipPath:
                'polygon(74.1% 44.1%, 100% 61.6%, 97.5% 26.9%, 85.5% 0.1%, 80.7% 2%, 72.5% 32.5%, 60.2% 62.4%, 52.4% 68.1%, 47.5% 58.3%, 45.2% 34.5%, 27.5% 76.7%, 0.1% 64.9%, 17.9% 100%, 27.6% 76.8%, 76.1% 97.7%, 74.1% 44.1%)',
            }}
          />
        </div>
        <div className="py-24 sm:py-32">
          <div className="mx-auto max-w-7xl px-6 lg:px-8">
            <div className="mx-auto max-w-2xl text-center">
              <motion.div 
                className="flex justify-center mb-8"
                initial={{ opacity: 0, scale: 0.5 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.5 }}
              >
                <CircleDot className="h-16 w-16" />
              </motion.div>
              <motion.h1
                className="text-4xl font-bold tracking-tight sm:text-6xl bg-gradient-to-r from-primary to-primary/50 bg-clip-text text-transparent"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.2 }}
              >
                智能标注 · 算力租用 · 教育管理
              </motion.h1>
              <motion.p
                className="mt-6 text-lg leading-8 text-muted-foreground"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.4 }}
              >
                专业的AI数据标注服务、灵活的云计算资源租用、完整的教育培训管理解决方案。助力企业数字化转型，推动人工智能产业发展。
              </motion.p>
              <motion.div 
                className="mt-10 flex items-center justify-center gap-x-6"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.6 }}
              >
                <Button size="lg" asChild className="group">
                  <Link href="/products">
                    探索产品 
                    <ArrowRight className="ml-2 h-4 w-4 transition-transform group-hover:translate-x-1" />
                  </Link>
                </Button>
                <Button variant="outline" size="lg">
                  了解更多
                </Button>
              </motion.div>
            </div>
          </div>
        </div>
      </div>

      {/* Data Statistics Section */}
      <motion.div 
        className="relative -mt-12 pb-20"
        initial={{ opacity: 0, y: 20 }}
        whileInView={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        viewport={{ once: true }}
      >
        <div className="mx-auto max-w-7xl px-6 lg:px-8">
          <div className="mx-auto max-w-4xl rounded-3xl bg-muted/30 backdrop-blur-lg ring-1 ring-primary/10 p-8">
            <div className="grid grid-cols-2 gap-8 md:grid-cols-4">
              {stats.map((stat) => (
                <div key={stat.label} className="text-center">
                  <div className="text-3xl font-bold bg-gradient-to-r from-primary to-primary/60 bg-clip-text text-transparent">
                    {stat.value}
                  </div>
                  <div className="mt-2 text-sm text-muted-foreground">
                    {stat.label}
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </motion.div>

      {/* Features Section */}
      <div className="py-24 sm:py-32 bg-gradient-to-b from-muted/50 to-background">
        <div className="mx-auto max-w-7xl px-6 lg:px-8">
          <div className="mx-auto max-w-2xl lg:text-center">
            <h2 className="text-base font-semibold leading-7 text-primary">技术创新</h2>
            <p className="mt-2 text-3xl font-bold tracking-tight sm:text-4xl">
              引领行业数字化转型
            </p>
            <p className="mt-6 text-lg leading-8 text-muted-foreground">
              秉持&quot;技术引领未来，创新驱动发展&quot;的理念，我们致力于将前沿科技转化为实际应用，为客户创造价值。
            </p>
          </div>
          <div className="mx-auto mt-16 max-w-2xl sm:mt-20 lg:mt-24 lg:max-w-none">
            <dl className="grid max-w-xl grid-cols-1 gap-x-8 gap-y-16 lg:max-w-none lg:grid-cols-3">
              {features.map((feature) => (
                <FeatureCard key={feature.name} feature={feature} />
              ))}
            </dl>
          </div>
        </div>
      </div>

      {/* Solutions Section */}
      <div className="py-24 sm:py-32">
        <div className="mx-auto max-w-7xl px-6 lg:px-8">
          <div className="mx-auto max-w-2xl lg:text-center">
            <h2 className="text-base font-semibold leading-7 text-primary">行业赋能</h2>
            <p className="mt-2 text-3xl font-bold tracking-tight sm:text-4xl">
              定制化解决方案
            </p>
            <p className="mt-6 text-lg leading-8 text-muted-foreground">
              通过跨界融合与协同创新，为多个行业提供智能化解决方案，助力传统产业升级转型。
            </p>
          </div>
          <div className="mx-auto mt-16 grid max-w-2xl grid-cols-1 gap-8 lg:max-w-none lg:grid-cols-3">
            {solutions.map((solution) => (
              <SolutionCard key={solution.title} solution={solution} />
            ))}
          </div>
        </div>
      </div>

      {/* Testimonials Section */}
      <div className="py-24 sm:py-32 bg-background">
        <div className="mx-auto max-w-7xl px-6 lg:px-8">
          <div className="mx-auto max-w-xl text-center">
            <h2 className="text-lg font-semibold leading-8 text-primary">用户反馈</h2>
            <p className="mt-2 text-3xl font-bold tracking-tight sm:text-4xl">
              值得信赖的技术伙伴
            </p>
          </div>
          <div className="mx-auto mt-16 grid max-w-2xl grid-cols-1 gap-8 lg:mx-0 lg:max-w-none lg:grid-cols-2">
            {testimonials.map((testimonial) => (
              <TestimonialCard key={testimonial.author} testimonial={testimonial} />
            ))}
          </div>
        </div>
      </div>

      {/* Partners Section */}
      <div className="py-24 sm:py-32 bg-muted/30">
        <div className="mx-auto max-w-7xl px-6 lg:px-8">
          <div className="mx-auto max-w-2xl lg:text-center">
            <h2 className="text-base font-semibold leading-7 text-primary">合作伙伴</h2>
            <p className="mt-2 text-3xl font-bold tracking-tight sm:text-4xl">
              值得信赖的技术生态
            </p>
          </div>
          <div className="mx-auto mt-16 grid max-w-lg grid-cols-4 items-center gap-x-8 gap-y-12 sm:max-w-xl sm:grid-cols-6 sm:gap-x-10 sm:gap-y-14 lg:max-w-4xl lg:grid-cols-5">
            {[1, 2, 3, 4, 5].map((i) => (
              <motion.div
                key={i}
                initial={{ opacity: 0, scale: 0.8 }}
                whileInView={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.5 }}
                viewport={{ once: true }}
              >
                <Image
                  className="col-span-2 max-h-12 w-full object-contain opacity-50 hover:opacity-100 transition-opacity lg:col-span-1"
                  src={`https://picsum.photos/seed/company${i}/158/48`}
                  alt={`Partner ${i}`}
                  width={158}
                  height={48}
                />
              </motion.div>
            ))}
          </div>
        </div>
      </div>

      {/* CTA Section */}
      <div className="relative isolate mt-32 px-6 py-32 sm:mt-40 sm:py-40 lg:px-8">
        <div className="absolute inset-0 -z-10 overflow-hidden">
          <div className="absolute inset-0 bg-gradient-to-br from-primary/5 to-primary/10 backdrop-blur-3xl" />
        </div>
        <div className="mx-auto max-w-2xl text-center">
          <motion.h2 
            className="text-3xl font-bold tracking-tight sm:text-4xl"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            viewport={{ once: true }}
          >
            准备好开启数字化转型了吗？
          </motion.h2>
          <motion.p 
            className="mx-auto mt-6 max-w-xl text-lg leading-8 text-muted-foreground"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.2 }}
            viewport={{ once: true }}
          >
            加入数千家企业的行列，探索零点科技如何帮助您实现业务创新。
          </motion.p>
          <motion.div 
            className="mt-10 flex items-center justify-center gap-x-6"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.4 }}
            viewport={{ once: true }}
          >
            <Button size="lg" asChild>
              <Link href="/contact-us">预约咨询</Link>
            </Button>
            <Button variant="outline" size="lg">
              查看案例
            </Button>
          </motion.div>
        </div>
      </div>
    </>
  )
}