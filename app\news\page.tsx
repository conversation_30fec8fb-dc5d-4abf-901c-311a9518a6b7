import { Button } from "@/components/ui/button"
import { ArrowRight } from "lucide-react"
import Image from "next/image"
import Link from "next/link"

const news = [
  {
    id: "1",
    title: "零点科技完成新一轮融资，加速AI技术创新",
    summary: "零点科技今日宣布完成新一轮融资，将进一步加大在人工智能领域的研发投入，推动技术创新和产品升级。",
    date: "2024-03-20",
    category: "公司新闻",
    image: "https://picsum.photos/seed/news1/800/400",
    readTime: "3分钟"
  },
  {
    id: "2",
    title: "零点科技荣获“2024年度最具创新力企业”奖项",
    summary: "在昨日举办的2024科技创新峰会上，零点科技凭借在AI领域的突出贡献获得最具创新力企业奖。",
    date: "2024-03-15",
    category: "行业动态",
    image: "https://picsum.photos/seed/news2/800/400",
    readTime: "4分钟"
  },
  // 可以添加更多新闻...
]

export default function NewsPage() {
  return (
    <div className="py-24 sm:py-32">
      <div className="mx-auto max-w-7xl px-6 lg:px-8">
        <div className="mx-auto max-w-2xl text-center">
          <h2 className="text-3xl font-bold tracking-tight sm:text-4xl">新闻中心</h2>
          <p className="mt-2 text-lg leading-8 text-muted-foreground">
            了解零点科技最新动态与行业资讯
          </p>
        </div>
        
        <div className="mx-auto mt-16 grid max-w-2xl grid-cols-1 gap-8 lg:mx-0 lg:max-w-none lg:grid-cols-3">
          {news.map((item) => (
            <article 
              key={item.id}
              className="relative isolate flex flex-col justify-end overflow-hidden rounded-2xl bg-gray-900 px-8 pb-8 pt-80 sm:pt-48 lg:pt-80"
            >
              <Image
                src={item.image}
                alt={item.title}
                fill
                className="absolute inset-0 -z-10 h-full w-full object-cover"
              />
              <div className="absolute inset-0 -z-10 bg-gradient-to-t from-gray-900 via-gray-900/40" />
              <div className="absolute inset-0 -z-10 rounded-2xl ring-1 ring-inset ring-gray-900/10" />

              <div className="flex flex-wrap items-center gap-y-1 overflow-hidden text-sm leading-6 text-gray-300">
                <time dateTime={item.date} className="mr-8">{item.date}</time>
                <div className="-ml-4 flex items-center gap-x-4">
                  <span className="inline-flex items-center rounded-full bg-white/10 px-3 py-1 text-xs font-medium text-white">
                    {item.category}
                  </span>
                  <span>{item.readTime}</span>
                </div>
              </div>
              <h3 className="mt-3 text-lg font-semibold leading-6 text-white">
                <Link href={`/news/${item.id}`}>
                  <span className="absolute inset-0" />
                  {item.title}
                </Link>
              </h3>
              <p className="mt-2 text-sm/relaxed text-gray-300 line-clamp-3">
                {item.summary}
              </p>
            </article>
          ))}
        </div>
      </div>
    </div>
  )
}
