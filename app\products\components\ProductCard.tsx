"use client"
import { <PERSON>, <PERSON><PERSON>ontent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import Link from "next/link"
import { ArrowRight, LucideIcon } from "lucide-react"
import { cn } from "@/lib/utils"
import dynamic from 'next/dynamic'
import { LucideProps } from 'lucide-react'
import { lazy } from "react"




interface ProductFeature {
  text: string
  iconName: IconName
}

interface Product {
  slug: string
  name: string
  description: string
  iconName: IconName
  features: ProductFeature[]
  highlight?: string
}

interface ProductCardProps {
  product: Product
  index: number
}

type IconName = string;

const loadIcon = async (iconName: string) => {
  try {
    const mod = await import('lucide-react');
    const Icon = mod[iconName as keyof typeof mod] as LucideIcon;
    return Icon || null;
  } catch (error) {
    console.error(`Error loading icon: ${iconName}`, error);
    return null;
  }
};

interface IconWrapperProps extends LucideProps {
  iconName: string;
}

const IconWrapper = ({ iconName, ...props }: IconWrapperProps) => {
  const DynamicIcon = dynamic<LucideProps>(
    () => loadIcon(iconName).then(Icon => Icon ? Icon : () => null),
    { loading: () => <div className="w-6 h-6 animate-pulse bg-primary/10 rounded" /> }
  );
  return <DynamicIcon {...props} />;
};

export function ProductCard({ product, index }: ProductCardProps) {
  return (
    <Card 
      className={cn(
        "flex flex-col transition-all duration-500 hover:shadow-xl group",
        "backdrop-blur-sm bg-gradient-to-br from-muted/50 to-muted/30",
        product.highlight && "border-primary/50 shadow-lg relative overflow-hidden",
        "animate-in fade-in duration-700 slide-in-from-bottom-4",
        `delay-[${index * 100}ms]`
      )}
    >
      {product.highlight && (
        <div className="absolute inset-0 bg-gradient-to-br from-primary/5 to-transparent" />
      )}
      
      <CardHeader className="relative">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="p-3 bg-gradient-to-br from-primary/20 to-primary/10 rounded-xl group-hover:scale-110 transition-transform duration-300">
              <IconWrapper iconName={product.iconName} className="h-6 w-6 text-primary" />
            </div>
            <CardTitle className="text-lg group-hover:text-primary transition-colors">
              {product.name}
            </CardTitle>
          </div>
          {product.highlight && (
            <span className="px-3 py-1 text-xs font-medium bg-primary/10 text-primary rounded-full animate-pulse">
              {product.highlight}
            </span>
          )}
        </div>
        <CardDescription className="mt-4 line-clamp-2">
          {product.description}
        </CardDescription>
      </CardHeader>
      
      <CardContent className="flex-1 flex flex-col relative">
        <ul className="space-y-4 mb-8">
          {product.features.map((feature) => (
            <li 
              key={feature.text} 
              className="flex items-center space-x-3 text-muted-foreground group/item"
            >
              <div className="p-1.5 rounded-lg bg-primary/5 group-hover/item:bg-primary/10 transition-colors">
                <IconWrapper iconName={feature.iconName} className="h-4 w-4 text-primary/60 group-hover/item:text-primary transition-colors" />
              </div>
              <span className="text-sm group-hover/item:text-foreground transition-colors">
                {feature.text}
              </span>
            </li>
          ))}
        </ul>
        
        <Button 
          asChild 
          className={cn(
            "w-full mt-auto group/btn",
            product.highlight 
              ? "bg-gradient-to-r from-primary to-primary/80 hover:from-primary/90 hover:to-primary/70" 
              : "border-primary/20 hover:border-primary/40"
          )}
          variant={product.highlight ? "default" : "outline"}
        >
          <Link href={`/products/${product.slug}`}>
            了解更多 
            <ArrowRight className="ml-2 h-4 w-4 transition-transform group-hover/btn:translate-x-1" />
          </Link>
        </Button>
      </CardContent>
    </Card>
  )
} 
