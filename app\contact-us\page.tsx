import { Card, CardContent } from "@/components/ui/card"

import { 
  Phone, 
  Mail, 
  MapPin, 
  MessageSquare,
  Building2,
  Headphones,
  Users
} from "lucide-react"
import ContactUsForm from './components/contactUsFrom'

const contactMethods = [
  {
    icon: Phone,
    title: "客服热线",
    value: "************",
    desc: "周一至周五 9:00-18:00"
  },
  {
    icon: Mail,
    title: "电子邮箱",
    value: "<EMAIL>",
    desc: "我们将在24小时内回复"
  },
  {
    icon: MapPin,
    title: "公司地址",
    value: "北京市朝阳区科技园区88号零点大厦",
    desc: "欢迎来访交流"
  }
]

const departments = [
  {
    icon: Headphones,
    title: "技术支持",
    email: "<EMAIL>",
    desc: "产品使用问题咨询"
  },
  {
    icon: Building2,
    title: "商务合作",
    email: "<EMAIL>",
    desc: "商业合作洽谈"
  },
  {
    icon: MessageSquare,
    title: "媒体咨询",
    email: "<EMAIL>",
    desc: "媒体采访及品牌合作"
  },
  {
    icon: Users,
    title: "人才招聘",
    email: "<EMAIL>",
    desc: "加入我们，共创未来"
  }
]

export default function ContactUs() {

  return (
    <div className="relative isolate py-24 sm:py-32">
      {/* 背景效果 */}
      <div
        className="absolute inset-x-0 top-0 -z-10 h-[1000px] transform-gpu overflow-hidden blur-3xl"
        aria-hidden="true"
      >
        <div
          className="relative left-[calc(50%-11rem)] aspect-[1155/678] w-[36.125rem] -translate-x-1/2 rotate-[30deg] bg-gradient-to-tr from-[#ff80b5] to-[#9089fc] opacity-30 sm:left-[calc(50%-30rem)] sm:w-[72.1875rem] animate-pulse"
          style={{
            clipPath:
              'polygon(74.1% 44.1%, 100% 61.6%, 97.5% 26.9%, 85.5% 0.1%, 80.7% 2%, 72.5% 32.5%, 60.2% 62.4%, 52.4% 68.1%, 47.5% 58.3%, 45.2% 34.5%, 27.5% 76.7%, 0.1% 64.9%, 17.9% 100%, 27.6% 76.8%, 76.1% 97.7%, 74.1% 44.1%)',
          }}
        />
      </div>

      <div className="mx-auto max-w-7xl px-6 lg:px-8">
        {/* 标题部分 */}
        <div className="mx-auto max-w-2xl text-center animate-fade-up">
          <h1 className="text-3xl font-bold tracking-tight sm:text-4xl bg-gradient-to-r from-primary to-primary/50 bg-clip-text text-transparent mb-4">
            联系我们
          </h1>
          <p className="text-lg leading-8 text-muted-foreground">
            无论您有任何问题或建议，我们都随时准备倾听和帮助
          </p>
        </div>

        {/* 联系方式卡片 */}
        <div className="mx-auto mt-16 grid max-w-4xl grid-cols-1 gap-6 sm:mt-20 md:grid-cols-3">
          {contactMethods.map((method, index) => (
            <Card 
              key={method.title}
              className="group hover:shadow-lg transition-all duration-300 border-0 bg-gradient-to-br from-muted/50 to-muted/30 backdrop-blur-sm animate-fade-up"
              style={{ animationDelay: `${index * 100}ms` }}
            >
              <CardContent className="p-6">
                <div className="flex items-center gap-4 mb-4">
                  <div className="p-2 rounded-lg bg-primary/10 group-hover:scale-110 transition-transform duration-300">
                    <method.icon className="h-5 w-5 text-primary" />
                  </div>
                  <h3 className="font-semibold group-hover:text-primary transition-colors">
                    {method.title}
                  </h3>
                </div>
                <p className="text-base font-medium mb-2">{method.value}</p>
                <p className="text-sm text-muted-foreground">{method.desc}</p>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* 联系表单 */}
        <ContactUsForm />

        {/* 部门联系方式 */}
        <div className="mx-auto max-w-4xl mt-20">
          <h2 className="text-xl font-semibold mb-8 text-center animate-fade-up">专业团队</h2>
          <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4">
            {departments.map((dept, index) => (
              <Card 
                key={dept.title}
                className="group hover:shadow-lg transition-all duration-300 border-0 bg-gradient-to-br from-muted/50 to-muted/30 backdrop-blur-sm animate-fade-up"
                style={{ animationDelay: `${index * 100}ms` }}
              >
                <CardContent className="p-6">
                  <div className="flex items-center gap-4 mb-4">
                    <div className="p-2 rounded-lg bg-primary/10 group-hover:scale-110 transition-transform duration-300">
                      <dept.icon className="h-5 w-5 text-primary" />
                    </div>
                    <h3 className="font-semibold group-hover:text-primary transition-colors">
                      {dept.title}
                    </h3>
                  </div>
                  <p className="text-base font-medium mb-2">{dept.email}</p>
                  <p className="text-sm text-muted-foreground">{dept.desc}</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </div>
    </div>
  )
} 